.members-list {
  margin: 0;
  padding: 0;
}

.members-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 1.5rem;
  text-align: center;
  border-bottom: 2px solid #007bff;
  padding-bottom: 0.5rem;
}

.members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.member-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid #e9ecef;
}

.member-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.member-image-container {
  text-align: center;
  margin-bottom: 1.5rem;
}

.member-image {
  width: 180px;
  height: 180px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #007bff;
  transition: border-color 0.2s ease;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
}

.member-card:hover .member-image {
  border-color: #0056b3;
}

.member-info {
  text-align: center;
}

.member-name {
  font-size: 1.3rem;
  font-weight: bold;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.member-role {
  display: inline-block;
  background: #007bff;
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 1rem;
}

.member-bio {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 1rem 0;
  text-align: left;
}

.member-details {
  margin: 1rem 0;
  padding: 1rem 0;
  border-top: 1px solid #e9ecef;
  border-bottom: 1px solid #e9ecef;
}

.member-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.member-detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-weight: 600;
  color: #495057;
  min-width: 80px;
}

.detail-value {
  color: #6c757d;
  text-align: right;
  flex: 1;
}

.member-social-links {
  margin-top: 1rem;
  padding-top: 1rem;
}

.social-label {
  display: block;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.social-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.social-button {
  display: inline-block;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  text-decoration: none;
  font-size: 0.85rem;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.social-button.twitter,
.social-button.x {
  background: #1da1f2;
  color: white;
}

.social-button.twitter:hover,
.social-button.x:hover {
  background: #0d8bd9;
  transform: translateY(-1px);
}

.social-button.instagram {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  color: white;
}

.social-button.instagram:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.social-button.youtube {
  background: #ff0000;
  color: white;
}

.social-button.youtube:hover {
  background: #cc0000;
  transform: translateY(-1px);
}

.social-button.tiktok {
  background: #000000;
  color: white;
}

.social-button.tiktok:hover {
  background: #333333;
  transform: translateY(-1px);
}

/* レスポンシブ対応 */
@media (max-width: 768px) {
  .members-grid {
    grid-template-columns: 1fr;
  }

  .member-card {
    padding: 1rem;
  }

  .member-image {
    width: 100px;
    height: 100px;
  }

  .members-list {
    padding: 1rem;
  }
}