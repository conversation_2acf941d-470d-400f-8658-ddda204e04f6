import React from 'react';
import { useAppContext } from '../context/AppContext';
import MembersList from '../components/MembersList';

function ComedianDetailPage({ comedianId }) {
  const { data } = useAppContext();
  const comedian = data.comedians.find(c => c.id === comedianId);
  const comedianEvents = data.events.filter(e => e.comedianId === comedian?.id && e.isApproved);
  const comedianArticles = data.articles.filter(a => a.comedianId === comedian?.id && a.isApproved);

  if (!comedian) return (
    <div className="text-center py-12">
      <div className="text-6xl mb-4">😅</div>
      <h2 className="text-2xl font-bold text-gray-800 mb-2">芸人が見つかりません</h2>
      <p className="text-gray-600">お探しの芸人は存在しないか、削除された可能性があります。</p>
    </div>
  );

  return (
    <div className="space-y-8">
      {/* プロフィールセクション */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 p-8 text-white">
          <div className="flex flex-col md:flex-row items-center space-y-6 md:space-y-0 md:space-x-8">
            <div className="relative">
              <img
                src={comedian.image}
                alt={comedian.name}
                className="w-32 h-32 object-cover rounded-2xl shadow-2xl ring-4 ring-white ring-opacity-50"
              />
            </div>
            <div className="text-center md:text-left">
              <h1 className="text-4xl font-bold mb-2">{comedian.name}</h1>
              <div className="bg-white bg-opacity-90 backdrop-blur-sm px-4 py-2 rounded-full inline-block mb-4 shadow-lg">
                <span className="text-sm font-medium text-purple-600">{comedian.category}</span>
              </div>
              <p className="text-lg opacity-90">{comedian.bio}</p>
            </div>
          </div>
        </div>
        <div className="p-8">
          <h4 className="text-lg font-semibold text-gray-800 mb-4">
            {comedian.members && comedian.members.length > 0 ? 'コンビ公式SNS' : 'SNSリンク'}
          </h4>
          <div className="flex flex-wrap gap-3">
            {comedian.socialLinks.map((link, index) => (
              <a
                key={index}
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 bg-gradient-to-r from-gray-100 to-gray-200 hover:from-purple-100 hover:to-blue-100 px-4 py-2 rounded-full transition-all"
              >
                <span>
                  {link.platform === 'Twitter' || link.platform === 'X' ? '🐦' :
                    link.platform === 'Instagram' ? '📷' :
                      link.platform === 'YouTube' ? '🎥' :
                        link.platform === '公式サイト' ? '🌐' : '🔗'}
                </span>
                <span className="text-sm font-medium text-gray-700">{link.platform}</span>
              </a>
            ))}
          </div>

          {/* コンビ詳細情報 */}
          {comedian.members && comedian.members.length > 0 && (
            <div className="mt-8 pt-8 border-t border-gray-200">
              <h4 className="text-lg font-semibold text-gray-800 mb-4">コンビ情報</h4>
              <div className="space-y-3">
                {comedian.formationYear && (
                  <div className="flex items-center space-x-2 text-gray-600">
                    <span>📅</span>
                    <span className="font-bold">結成年:</span>
                    <span>{comedian.formationYear}</span>
                  </div>
                )}
                {comedian.activeYears && (
                  <div className="flex items-center space-x-2 text-gray-600">
                    <span>⏰</span>
                    <span className="font-bold">活動年:</span>
                    <span>{comedian.activeYears}</span>
                  </div>
                )}
                {comedian.formerName && comedian.formerName !== "なし" && (
                  <div className="flex items-center space-x-2 text-gray-600">
                    <span>🔄</span>
                    <span className="font-bold">旧コンビ名:</span>
                    <span>{comedian.formerName}</span>
                  </div>
                )}
                {comedian.contemporaries && (
                  <div className="flex items-start space-x-2 text-gray-600">
                    <span>👥</span>
                    <span className="font-bold">同期:</span>
                    <span>{comedian.contemporaries}</span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* メンバー情報セクション */}
      {comedian.members && comedian.members.length > 0 && (
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
          <MembersList members={comedian.members} />
        </div>
      )}

      {/* 出演情報セクション */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
        <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
          <span className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mr-3">
            <span className="text-white text-sm">📅</span>
          </span>
          出演情報
        </h3>
        {comedianEvents.length > 0 ? (
          <div className="space-y-4">
            {comedianEvents.map(event => (
              <div key={event.id} className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-100 rounded-xl p-6">
                <h4 className="text-xl font-semibold text-gray-800 mb-3">{event.eventName}</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2 text-gray-600">
                    <span>📅</span>
                    <span>{event.date}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-600">
                    <span>📍</span>
                    <span>{event.venue}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-600">
                    <span>👤</span>
                    <span>{event.submittedBy}</span>
                  </div>
                  {event.detailsURL && (
                    <div className="flex items-center space-x-2">
                      <a
                        href={event.detailsURL}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 rounded-full text-sm hover:from-green-600 hover:to-emerald-600 transition-all"
                      >
                        詳細を見る
                      </a>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">📅</div>
            <p className="text-gray-600">承認済みの出演情報はありません</p>
          </div>
        )}
      </div>

      {/* 関連記事セクション */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
        <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
          <span className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mr-3">
            <span className="text-white text-sm">📰</span>
          </span>
          関連記事
        </h3>
        {comedianArticles.length > 0 ? (
          <div className="space-y-6">
            {comedianArticles.map(article => (
              <div key={article.id} className="bg-gradient-to-r from-orange-50 to-red-50 border border-orange-100 rounded-xl p-6">
                {article.imageURL && (
                  <img
                    src={article.imageURL}
                    alt={article.title}
                    className="w-full h-48 object-cover rounded-lg mb-4"
                  />
                )}
                <h4 className="text-xl font-semibold text-gray-800 mb-3">{article.title}</h4>
                <p className="text-gray-600 leading-relaxed mb-4">{article.content}</p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 text-gray-500 text-sm">
                    <span>👤</span>
                    <span>{article.submittedBy}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">📰</div>
            <p className="text-gray-600">承認済みの記事はありません</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default ComedianDetailPage;
